const isEqual = require("lodash.isequal")

/**
 * 智能数据更新服务
 * 实现数据比较检查，避免不必要的setData操作，提升小程序性能
 */

/**
 * 浅层数据比较 - 适用于简单数据类型和一级对象属性
 * @param {Object} newData 新数据
 * @param {Object} currentData 当前数据
 * @returns {boolean} 数据是否发生变化
 */
function hasShallowDataChanged(newData, currentData) {
  if (!newData || !currentData) {
    return true
  }

  const newKeys = Object.keys(newData)
  const currentKeys = Object.keys(currentData)

  // 检查键的数量是否相同
  if (newKeys.length !== currentKeys.length) {
    return true
  }

  // 检查每个键的值是否相同
  for (const key of newKeys) {
    if (newData[key] !== currentData[key]) {
      return true
    }
  }

  return false
}

/**
 * 深度数据比较 - 适用于复杂嵌套对象和数组
 * @param {Object} newData 新数据
 * @param {Object} currentData 当前数据
 * @returns {boolean} 数据是否发生变化
 */
function hasDeepDataChanged(newData, currentData) {
  return !isEqual(newData, currentData)
}

/**
 * 关键字段比较 - 只比较影响UI渲染的关键字段
 * @param {Object} newData 新数据
 * @param {Object} currentData 当前数据
 * @param {Array} keyFields 需要比较的关键字段数组
 * @returns {boolean} 关键数据是否发生变化
 */
function hasKeyFieldsChanged(newData, currentData, keyFields) {
  if (!newData || !currentData || !Array.isArray(keyFields)) {
    return true
  }

  for (const field of keyFields) {
    if (!isEqual(newData[field], currentData[field])) {
      return true
    }
  }

  return false
}

/**
 * 智能setData - 只有数据发生变化时才执行setData
 * @param {Object} pageContext 页面上下文(this)
 * @param {Object} newData 要更新的新数据
 * @param {Object} options 配置选项
 * @param {string} options.compareType 比较类型: 'shallow'|'deep'|'keyFields'
 * @param {Array} options.keyFields 关键字段数组(当compareType为'keyFields'时使用)
 * @param {Function} options.callback setData完成后的回调函数
 * @returns {boolean} 是否执行了setData操作
 */
function smartSetData(pageContext, newData, options = {}) {
  const { compareType = "shallow", keyFields = [], callback } = options

  if (!pageContext || !newData || typeof pageContext.setData !== "function") {
    console.warn("smartSetData: 参数无效", { pageContext, newData })
    return false
  }

  const currentData = pageContext.data
  let hasChanged = false

  // 根据比较类型选择比较方法
  switch (compareType) {
    case "deep":
      hasChanged = hasDeepDataChanged(newData, currentData)
      break
    case "keyFields":
      hasChanged = hasKeyFieldsChanged(newData, currentData, keyFields)
      break
    case "shallow":
    default:
      hasChanged = hasShallowDataChanged(newData, currentData)
      break
  }

  // 只有数据发生变化时才执行setData
  if (hasChanged) {
    pageContext.setData(newData, callback)
    console.log("smartSetData: 数据已更新", {
      compareType,
      updatedFields: Object.keys(newData),
    })
    return true
  } else {
    console.log("smartSetData: 数据无变化，跳过setData", {
      compareType,
      checkedFields: Object.keys(newData),
    })
    if (callback && typeof callback === "function") {
      callback()
    }
    return false
  }
}

/**
 * 列表数据智能更新 - 专门用于列表数据的比较和更新
 * @param {Object} pageContext 页面上下文(this)
 * @param {Array} newList 新的列表数据
 * @param {string} listFieldName 列表在data中的字段名
 * @param {Object} options 配置选项
 * @param {Array} options.compareFields 用于比较的字段，默认比较整个对象
 * @param {Function} options.callback 更新完成后的回调
 * @returns {boolean} 是否执行了setData操作
 */
function smartUpdateList(pageContext, newList, listFieldName, options = {}) {
  const { compareFields, callback } = options

  if (!pageContext || !Array.isArray(newList) || !listFieldName) {
    console.warn("smartUpdateList: 参数无效", {
      pageContext,
      newList,
      listFieldName,
    })
    return false
  }

  const currentList = pageContext.data[listFieldName] || []

  // 比较列表长度
  if (newList.length !== currentList.length) {
    pageContext.setData({ [listFieldName]: newList }, callback)
    console.log("smartUpdateList: 列表长度变化，已更新", {
      field: listFieldName,
      oldLength: currentList.length,
      newLength: newList.length,
    })
    return true
  }

  // 比较列表内容
  let hasListChanged = false

  if (compareFields && Array.isArray(compareFields)) {
    // 只比较指定字段
    for (let i = 0; i < newList.length; i++) {
      for (const field of compareFields) {
        if (!isEqual(newList[i][field], currentList[i][field])) {
          hasListChanged = true
          break
        }
      }
      if (hasListChanged) break
    }
  } else {
    // 比较整个列表
    hasListChanged = !isEqual(newList, currentList)
  }

  if (hasListChanged) {
    pageContext.setData({ [listFieldName]: newList }, callback)
    console.log("smartUpdateList: 列表内容变化，已更新", {
      field: listFieldName,
    })
    return true
  } else {
    console.log("smartUpdateList: 列表无变化，跳过setData", {
      field: listFieldName,
    })
    if (callback && typeof callback === "function") {
      callback()
    }
    return false
  }
}

/**
 * 网络请求响应智能处理 - 专门用于处理API响应数据
 * @param {Object} pageContext 页面上下文(this)
 * @param {Object} apiResponse API响应数据
 * @param {Object} config 配置选项
 * @param {string} config.listField 列表字段名(如'articleList', 'newsArticleList')
 * @param {string} config.loadingField 加载状态字段名(如'noticeLoading', 'examLoading')
 * @param {string} config.hasMoreField 是否有更多数据字段名(如'noticeHasMore', 'examHasMore')
 * @param {boolean} config.isLoadMore 是否为加载更多操作
 * @param {Array} config.compareFields 用于比较的关键字段
 * @param {Function} config.dataProcessor 数据处理函数
 * @returns {boolean} 是否执行了数据更新
 */
function smartHandleApiResponse(pageContext, apiResponse, config = {}) {
  const {
    listField,
    loadingField,
    hasMoreField,
    isLoadMore = false,
    compareFields,
    dataProcessor,
  } = config

  if (!pageContext || !apiResponse) {
    console.warn("smartHandleApiResponse: 参数无效")
    return false
  }

  // 检查API响应是否成功
  if (!apiResponse.error || apiResponse.error.code !== 0 || !apiResponse.data) {
    console.log("smartHandleApiResponse: API响应失败或无数据")
    return false
  }

  const newList = apiResponse.data.list || []
  let updatedList

  if (isLoadMore) {
    // 分页加载：追加到现有数据
    const currentList = pageContext.data[listField] || []
    updatedList = [...currentList, ...newList]
  } else {
    // 首次加载或筛选：直接使用新数据
    updatedList = newList
  }

  // 处理数据（如果提供了数据处理函数）
  if (dataProcessor && typeof dataProcessor === "function") {
    updatedList = dataProcessor(updatedList)
  }

  // 构建要更新的数据对象
  const updateData = {}

  if (listField) {
    updateData[listField] = updatedList
  }

  if (loadingField) {
    updateData[loadingField] = false
  }

  if (hasMoreField) {
    updateData[hasMoreField] = newList.length > 0
  }

  // 使用智能更新
  return smartSetData(pageContext, updateData, {
    compareType: compareFields ? "keyFields" : "shallow",
    keyFields: compareFields,
  })
}

/**
 * 筛选条件智能更新 - 专门用于处理筛选条件的更新
 * @param {Object} pageContext 页面上下文(this)
 * @param {string} filterField 筛选条件字段名(如'noticeSelectForTemplate')
 * @param {string} key 筛选项的key
 * @param {*} value 筛选项的值
 * @param {Object} options 配置选项
 * @param {Function} options.callback 更新完成后的回调
 * @returns {boolean} 是否执行了setData操作
 */
function smartUpdateFilter(pageContext, filterField, key, value, options = {}) {
  const { callback } = options

  if (!pageContext || !filterField || !key) {
    console.warn("smartUpdateFilter: 参数无效")
    return false
  }

  const currentFilter = pageContext.data[filterField] || {}
  const newFilter = {
    ...currentFilter,
    [key]: value,
  }

  // 检查筛选条件是否真的发生了变化
  if (isEqual(currentFilter[key], value)) {
    console.log("smartUpdateFilter: 筛选条件无变化，跳过更新", {
      filterField,
      key,
    })
    if (callback && typeof callback === "function") {
      callback()
    }
    return false
  }

  return smartSetData(pageContext, { [filterField]: newFilter }, { callback })
}

/**
 * 批量智能更新 - 用于同时更新多个字段
 * @param {Object} pageContext 页面上下文(this)
 * @param {Array} updates 更新配置数组
 * @param {Object} options 配置选项
 * @returns {boolean} 是否执行了setData操作
 */
function smartBatchUpdate(pageContext, updates, options = {}) {
  if (!pageContext || !Array.isArray(updates) || updates.length === 0) {
    console.warn("smartBatchUpdate: 参数无效")
    return false
  }

  const updateData = {}
  let hasAnyChange = false

  // 收集所有需要更新的数据
  for (const update of updates) {
    const { field, value, compareType = "shallow" } = update

    if (!field) continue

    const currentValue = pageContext.data[field]
    let hasChanged = false

    switch (compareType) {
      case "deep":
        hasChanged = hasDeepDataChanged(
          { [field]: value },
          { [field]: currentValue }
        )
        break
      case "shallow":
      default:
        hasChanged = value !== currentValue
        break
    }

    if (hasChanged) {
      updateData[field] = value
      hasAnyChange = true
    }
  }

  // 只有有变化时才执行setData
  if (hasAnyChange) {
    pageContext.setData(updateData, options.callback)
    console.log("smartBatchUpdate: 批量更新完成", {
      updatedFields: Object.keys(updateData),
    })
    return true
  } else {
    console.log("smartBatchUpdate: 无数据变化，跳过更新")
    if (options.callback && typeof options.callback === "function") {
      options.callback()
    }
    return false
  }
}

module.exports = {
  hasShallowDataChanged,
  hasDeepDataChanged,
  hasKeyFieldsChanged,
  smartSetData,
  smartUpdateList,
  smartHandleApiResponse,
  smartUpdateFilter,
  smartBatchUpdate,
}
